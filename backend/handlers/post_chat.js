import { Readable } from 'stream';
import { sendMessage } from '../services/internal_api_ai.js';
import { isVerified } from '../services/recaptcha.js';
import { createPostSink } from '../services/sink.js';
import { sendMessage as sendSlackMessage } from '../services/slack.js';

const schema = {
  body: {
    type: 'object',
    required: ['post', 'recaptchaToken'],
    properties: {
      post: {
        type: 'string',
        maxLength: 500,
      },
      messages: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            role: { type: 'string' },
            content: { type: 'string' },
          },
        },
      },
      path: { type: 'string' },
      threadTs: {
        type: 'string',
        nullable: true,
      },
      recaptchaToken: { type: 'string' },
      lang: {
        type: 'string',
        enum: ['ja', 'en'],
        default: 'ja',
      },
    },
  },
};

const GREETING = {
  ja: ['お問い', '合わせ', 'ありが', 'とう', 'ござ', 'います', '。', '\n\n'],
  en: ['Thank ', 'you ', 'for ', 'your ', 'inquiry', '.', '\n\n'],
};
const FIRST_WAITING = 1500;
const INTERVAL = 120;

const generateGreetingStream = ({ isRequired, lang = 'ja' }) => {
  if (!isRequired) {
    return new Readable({
      read() {
        this.push(null);
      },
    });
  }
  let idx = 0;
  return new Readable({
    read(size) {
      const interval = idx === 0 ? FIRST_WAITING : INTERVAL;
      if (idx < GREETING[lang].length) {
        setTimeout(() => {
          this.push(GREETING[lang][idx]);
          idx++;
        }, interval);
      } else {
        this.push(null);
      }
    },
  });
};

const generateTsStream = (ts) => {
  return new Readable({
    read(size) {
      this.push(`${ts}\n`);
      this.push(null);
    },
  });
};

const handler = async (request, reply) => {
  const { post, messages, path, threadTs, recaptchaToken, lang } = request.body;

  if (!(await isVerified(recaptchaToken))) {
    return reply.status(400).send({ status: 'error', message: 'Invalid reCAPTCHA token' });
  }

  const clientIp = request.headers['cf-connecting-ip'];
  const dataPromise = sendMessage(post, messages, path, clientIp, lang);

  const channelId = process.env.SLACK_CHAT_CHANNEL_ID;
  const { ts } = await sendSlackMessage(process.env.SECRET_SLACK_USER_TOKEN, channelId, post, threadTs);

  const headers = reply.getHeaders();
  Object.keys(headers).forEach((key) => {
    reply.raw.header(key, headers[key]);
  });

  const tsStream = generateTsStream(threadTs ?? ts);
  tsStream.pipe(reply.raw, { end: false });

  tsStream.on('end', async () => {
    const greetingStream = generateGreetingStream({ isRequired: !threadTs, lang });
    greetingStream.pipe(reply.raw, { end: false });

    let fullMessage = '';
    greetingStream.on('end', async () => {
      const data = await dataPromise;
      data.pipe(reply.raw, { end: true });

      data.on('end', async () => {
        reply.raw.end();
        const response = await sendSlackMessage(process.env.SECRET_SLACK_AI_TOKEN, channelId, fullMessage, threadTs ?? ts);
        createPostSink(threadTs ?? ts, response, messages, post, fullMessage, path ?? '', lang);
      });

      data.on('error', (err) => {
        console.error(err);
      });
      data.on('data', (chunk) => {
        fullMessage += chunk.toString();
      });
    });
    greetingStream.on('error', (err) => {
      console.error(err);
    });

    greetingStream.on('data', (chunk) => {
      fullMessage += chunk.toString();
    });
  });
  tsStream.on('error', (err) => {
    console.error(err);
  });

  return reply;
};

export default { schema, handler };
