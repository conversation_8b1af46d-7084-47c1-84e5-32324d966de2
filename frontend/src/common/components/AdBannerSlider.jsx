import { Trans, useTranslation } from 'react-i18next';
import { useMediaQuery } from 'react-responsive';
import VerticalSlider from './VerticalSlider';

function AdBannerSlider() {
  const { t } = useTranslation('common');

  return (
    <VerticalSlider
      items={[
        <a
          key="top_devday2025_link"
          id="top_devday2025_link"
          href="https://www.gmo.jp/news/article/9594/"
          target="_blank"
          rel="noopener"
          className="c-newsLink"
        >
          <span className="icon-base icon-size24 icon-sec-news c-newsLink__icon" />
          <div className="c-newsLink__textOmit">
            {t('DevDay2025 Security Night開催！')}
          </div>
        </a>,
        <a
          key="top_defcon_link"
          id="top_defcon_link"
          href="https://www.gmo.jp/news/article/9592/"
          target="_blank"
          rel="noopener"
          className="c-newsLink"
        >
          <span className="icon-base icon-size24 icon-sec-news c-newsLink__icon" />
          <div className="c-newsLink__textOmit">
            <Trans
              ns="common"
              i18nKey="GMOホワイトハッカー、<break>DEFCON出陣！</break>"
              components={{ break: <span className="c-newsLink__textBreak" /> }}
            />
          </div>
        </a>,
        <a
          key="top_locked_link"
          id="top_locked_link"
          href="https://www.gmo.jp/news/article/9600/"
          target="_blank"
          rel="noopener"
          className="c-newsLink"
        >
          <span className="icon-base icon-size24 icon-sec-news c-newsLink__icon" />
          <div className="c-newsLink__textOmit">
            <Trans
              ns="common"
              i18nKey="世界最大規模の国際サイバー防衛演習<break>に初参加</break>"
              components={{ break: <span className="c-newsLink__textBreak" /> }}
            />
          </div>
        </a>,
      ]}
      itemHeight={useMediaQuery({ query: '(max-width: 600px)' }) ? 50 : 26}
      visibleCount={1}
    />
  );
}

export default AdBannerSlider;
